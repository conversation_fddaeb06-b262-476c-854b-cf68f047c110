<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>CV Analysis Result #{{ result.id }} - CVScreenify</title>
  <style>
    :root {
      --primary: #2563eb;
      --primary-light: #3b82f6;
      --success: #10b981;
      --danger: #ef4444;
      --warning: #f59e0b;
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
      color: var(--gray-800);
      line-height: 1.6;
      padding: 20px;
      min-height: 100vh;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      border-radius: 16px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
      color: white;
      padding: 2rem;
      text-align: center;
    }

    .header h1 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .header .meta {
      font-size: 1rem;
      opacity: 0.9;
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;
    }

    .content {
      padding: 2rem;
    }

    .section {
      margin-bottom: 1.5rem;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 3px solid var(--primary);
      display: inline-block;
    }



    .job-description {
      background: var(--gray-50);
      border-left: 4px solid var(--primary);
      padding: 1.5rem;
      border-radius: 8px;
      font-size: 1rem;
      line-height: 1.7;
      white-space: pre-wrap;
    }

    /* Simple analysis content - no special styling */
    #analysis-content {
      /* No special styling - let content flow naturally */
    }
      margin: 0 !important;
      padding: 0 !important;
    }

    .analysis-content .strengths-weaknesses:first-child {
      margin-top: 0 !important;
      padding-top: 0 !important;
    }

    .rating-section {
      background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    .rating-score {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
    }

    .rating-label {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .strengths-weaknesses {
      background: var(--gray-50);
      border-left: 4px solid var(--primary);
      padding: 1.5rem;
      border-radius: 8px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    .strengths, .weaknesses {
      margin: 0;
      padding: 0;
    }

    .strengths {
      border-right: 1px solid var(--gray-200);
    }

    .strengths h3 {
      color: var(--success);
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .weaknesses h3 {
      color: var(--danger);
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .strengths ul, .weaknesses ul {
      list-style: none;
      padding: 0;
    }

    .strengths li, .weaknesses li {
      padding: 0.5rem 0;
      padding-left: 1.5rem;
      position: relative;
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .strengths li::before {
      content: "✓";
      position: absolute;
      left: 0;
      color: var(--success);
      font-weight: bold;
      font-size: 1.1rem;
    }

    .weaknesses li::before {
      content: "⚠";
      position: absolute;
      left: 0;
      color: var(--danger);
      font-size: 1rem;
    }

    .decision-section {
      background: var(--gray-50);
      padding: 1.5rem;
      border-top: 1px solid var(--gray-200);
    }

    .decision-title {
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: var(--gray-900);
    }

    .decision-content {
      font-size: 1.05rem;
      line-height: 1.6;
    }

    .decision-yes {
      color: var(--success);
      font-weight: 600;
    }

    .decision-no {
      color: var(--danger);
      font-weight: 600;
    }

    .actions {
      background: var(--gray-50);
      padding: 1.5rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      font-size: 0.95rem;
      transition: all 0.2s ease;
      border: none;
      cursor: pointer;
    }

    .btn-primary {
      background: var(--primary);
      color: white;
    }

    .btn-primary:hover {
      background: var(--primary-light);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: var(--gray-200);
      color: var(--gray-700);
    }

    .btn-secondary:hover {
      background: var(--gray-300);
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
        border-radius: 12px;
      }

      .header {
        padding: 1.5rem;
      }

      .header h1 {
        font-size: 1.5rem;
      }

      .header .meta {
        flex-direction: column;
        gap: 0.5rem;
      }

      .content {
        padding: 1.5rem;
      }

      .strengths-weaknesses {
        grid-template-columns: 1fr;
      }

      .strengths {
        border-right: none;
        border-bottom: 1px solid var(--gray-200);
      }

      .actions {
        flex-direction: column;
        align-items: stretch;
      }

      .btn {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <div class="header">
      <h1>CV Analysis Result #{{ result.id }}</h1>
      <div class="meta">
        <span>📅 {{ result.timestamp }}</span>
        {% if result.filename %}
        <span>📄 {{ result.filename }}</span>
        {% endif %}
        {% if result.processing_time %}
        <span>⏱️ {{ '%.2f'|format(result.processing_time) }}s processing time</span>
        {% endif %}
      </div>
    </div>

    <!-- Content Section -->
    <div class="content">
      <!-- Job Description Section -->
      <div class="section">
        <h2 class="section-title">📋 Job Description</h2>
        <div class="job-description">{{ fixed_job_desc }}</div>
      </div>

      <!-- Analysis Results Section -->
      <div class="section">
        <h2 class="section-title">🎯 Analysis Results</h2>
        <div id="analysis-content">
          <!-- Content will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Actions Section -->
    <div class="actions">
      <div>
        <a href="/results" class="btn btn-secondary">
          ← Back to Results
        </a>
      </div>
      <div>
        <a href="/download/{{ result.id }}" class="btn btn-primary">
          📄 Download PDF Report
        </a>
      </div>
    </div>
  </div>

  <script>
    // Parse and format the analysis content
    function parseAnalysis(analysisText) {
      const lines = analysisText.split('\n');
      let rating = null;
      let strengths = [];
      let weaknesses = [];
      let finalOpinion = '';

      let currentSection = '';

      for (let line of lines) {
        line = line.trim();
        if (!line) continue;

        // Extract rating with multiple patterns
        const ratingPatterns = [
          /Rating:\s*(\d+)\/10/i,
          /Rating:\s*(\d+)\s*out\s*of\s*10/i,
          /Score:\s*(\d+)\/10/i,
          /(\d+)\/10/
        ];

        for (let pattern of ratingPatterns) {
          const ratingMatch = line.match(pattern);
          if (ratingMatch) {
            rating = parseInt(ratingMatch[1]);
            break;
          }
        }

        // Detect sections
        if (line.toLowerCase().includes('strengths:')) {
          currentSection = 'strengths';
          continue;
        } else if (line.toLowerCase().includes('weaknesses:')) {
          currentSection = 'weaknesses';
          continue;
        } else if (line.toLowerCase().includes('final opinion:')) {
          currentSection = 'opinion';
          finalOpinion = line.replace(/final opinion:\s*/i, '');
          continue;
        }

        // Add items to current section
        if (line.startsWith('-') || line.startsWith('•')) {
          const item = line.substring(1).trim();
          if (currentSection === 'strengths') {
            strengths.push(item);
          } else if (currentSection === 'weaknesses') {
            weaknesses.push(item);
          }
        } else if (currentSection === 'opinion' && line) {
          finalOpinion += ' ' + line;
        }
      }

      // If no rating found, try to infer from decision
      if (rating === null) {
        if (finalOpinion.toLowerCase().includes('yes')) {
          rating = 7; // Default good rating for yes decisions
        } else {
          rating = 5; // Default lower rating for no decisions
        }
      }

      return { rating, strengths, weaknesses, finalOpinion };
    }

    function renderAnalysis() {
      const analysisText = `{{ fixed_analysis|safe }}`;
      const parsed = parseAnalysis(analysisText);
      const container = document.getElementById('analysis-content');

      let html = '';

      // Rating section - always show rating (even if not found)
      if (parsed.rating !== null) {
        const ratingColor = parsed.rating >= 7 ? 'var(--success)' : parsed.rating >= 5 ? 'var(--warning)' : 'var(--danger)';
        html += `<div class="rating-section" style="background: linear-gradient(135deg, ${ratingColor} 0%, ${ratingColor}dd 100%);">
            <div class="rating-score">${parsed.rating}/10</div>
            <div class="rating-label">Overall Match Score</div>
          </div>`;
      }

      // Strengths and Weaknesses
      if (parsed.strengths.length > 0 || parsed.weaknesses.length > 0) {
        html += '<div class="strengths-weaknesses" style="margin-top: 0 !important;">';

        // Strengths
        html += '<div class="strengths">';
        html += '<h3>💪 Strengths</h3>';
        if (parsed.strengths.length > 0) {
          html += '<ul>';
          parsed.strengths.forEach(strength => {
            html += `<li>${strength}</li>`;
          });
          html += '</ul>';
        } else {
          html += '<p style="color: var(--gray-600); font-style: italic;">No specific strengths identified.</p>';
        }
        html += '</div>';

        // Weaknesses
        html += '<div class="weaknesses">';
        html += '<h3>⚠️ Areas for Improvement</h3>';
        if (parsed.weaknesses.length > 0) {
          html += '<ul>';
          parsed.weaknesses.forEach(weakness => {
            html += `<li>${weakness}</li>`;
          });
          html += '</ul>';
        } else {
          html += '<p style="color: var(--gray-600); font-style: italic;">No significant weaknesses identified.</p>';
        }
        html += '</div>';

        html += '</div>';
      }

      // Final Decision - always show decision
      html += '<div class="decision-section">';
      html += '<h3 class="decision-title">🎯 Final Recommendation</h3>';

      let finalDecision = parsed.finalOpinion || '';

      // Ensure we have a clear Yes/No decision
      const isPositive = finalDecision.toLowerCase().includes('yes');
      const decisionClass = isPositive ? 'decision-yes' : 'decision-no';
      const decisionIcon = isPositive ? '✅' : '❌';

      // If no clear decision, infer from rating
      if (!finalDecision || (!finalDecision.toLowerCase().includes('yes') && !finalDecision.toLowerCase().includes('no'))) {
        if (parsed.rating >= 7) {
          finalDecision = `Yes - Strong candidate with ${parsed.rating}/10 rating, meets our hiring threshold.`;
        } else {
          finalDecision = `No - Candidate scored ${parsed.rating}/10, below our minimum threshold of 7/10.`;
        }
      }

      html += `<div class="decision-content">`;
      html += `<span class="${decisionClass}">${decisionIcon} ${finalDecision}</span>`;
      html += `</div>`;
      html += '</div>';

      // Fallback for unparsed content
      if (!html) {
        html = `
          <div style="padding: 2rem; text-align: center; color: var(--gray-600);">
            <p>Analysis content could not be parsed. Raw output:</p>
            <div style="background: var(--gray-50); padding: 1rem; border-radius: 8px; margin-top: 1rem; text-align: left; white-space: pre-wrap; font-family: monospace;">${analysisText}</div>
          </div>
        `;
      }

      container.innerHTML = html;

      // Debug: log what we generated
      console.log('Generated HTML:', html);

      // Force remove any spacing on first child
      const firstChild = container.firstElementChild;
      if (firstChild) {
        firstChild.style.marginTop = '0';
        firstChild.style.paddingTop = '0';
        console.log('First child:', firstChild);
      }
    }

    // Render the analysis when page loads
    document.addEventListener('DOMContentLoaded', renderAnalysis);
  </script>
</body>
</html>
