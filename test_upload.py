import requests
import json

# Test data
job_description = """We are looking for a Senior Software Engineer with 5+ years of experience in Python, JavaScript, and cloud technologies. Must have experience with React, Node.js, and AWS. Strong problem-solving skills required."""

# Read the test CV file
with open('test_cv_low.txt', 'rb') as f:
    cv_content = f.read()

# Prepare the form data
files = {'cv': ('test_cv_low.txt', cv_content, 'text/plain')}
data = {'job_description': job_description}

# Make the request
try:
    response = requests.post('http://127.0.0.1:5000/upload_single', files=files, data=data, timeout=60)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")
