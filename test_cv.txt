<PERSON>
Software Engineer

Education:
- Bachelor of Computer Science, MIT (2020-2024)
- CGPA: 3.8/4.0

Experience:
- Software Engineering Intern at Google (Summer 2023)
  - Developed microservices using Python and Django
  - Worked on machine learning algorithms for search optimization
  - Collaborated with cross-functional teams

- Full-stack Developer at TechStartup (2024-Present)
  - Built web applications using React and Node.js
  - Implemented RESTful APIs and database design
  - Led a team of 3 junior developers

Skills:
- Programming: Python, JavaScript, Java, C++
- Web Technologies: React, Node.js, Django, Flask
- Databases: PostgreSQL, MongoDB, Redis
- Cloud: AWS, Docker, Kubernetes
- Machine Learning: TensorFlow, PyTorch, scikit-learn

Projects:
- E-commerce Platform: Built a full-stack e-commerce application with payment integration
- AI Chatbot: Developed a customer service chatbot using NLP and machine learning
- Data Analytics Dashboard: Created real-time analytics dashboard for business metrics

Certifications:
- AWS Certified Solutions Architect
- Google Cloud Professional Data Engineer
