from flask import Flask, request, render_template, Response, redirect, url_for, jsonify
from flask_socketio import Socket<PERSON>, emit, join_room
import requests
import os
import logging
import json
import re
import sqlite3
from datetime import datetime
from PyPDF2 import PdfReader
from io import BytesIO
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
import re
import threading
import time
import tempfile
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import gc
from tqdm import tqdm
import queue
import uuid

def detokenize_text(text):
    """
    Convert the Llama output into proper sentences by:
      - Collapsing multiple whitespace characters into a single space.
      - Removing spaces before punctuation.
      - Inserting newlines before and after each section header.
    """
    # Collapse multiple whitespace (spaces, newlines) into one space.
    text = " ".join(text.split())
    # Remove extra spaces before punctuation.
    text = re.sub(r'\s+([.,:;!?])', r'\1', text)
    # Ensure section headers appear on their own line.
    for header in ["Rating:", "Strengths:", "Weaknesses:", "Final Opinion:"]:
        # Remove any spaces/newlines before the header and force a newline before it.
        text = re.sub(r'\s*' + re.escape(header), "\n" + header, text)
        # Also force a newline right after the header.
        text = re.sub(re.escape(header) + r'\s*', header + "\n", text)
    # Optionally, insert a newline after each period if it isn’t already present.
    text = re.sub(r'\. ', ".\n", text)
    return text.strip()



app = Flask(__name__)
app.config['SECRET_KEY'] = 'cvscreenify_secret_key_2024'

# Initialize SocketIO for real-time progress updates
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Configuration - Increased limits for bulk processing
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500 MB max total upload size

# Folder to store uploaded files temporarily
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Allowed file extensions for CV uploads
ALLOWED_EXTENSIONS = {'pdf', 'txt', 'docx'}

# SQLite database file for storing analysis results
DATABASE = 'results.db'

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Ollama API endpoint
OLLAMA_API_URL = "http://localhost:11434/api/generate"

# Global variables for batch processing
active_jobs = {}
job_results = {}
MAX_CONCURRENT_WORKERS = min(8, psutil.cpu_count())  # Limit based on CPU cores
BATCH_SIZE = 50  # Process files in batches to manage memory

# Helper Functions

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Initialize the SQLite database with enhanced schema and indexing for bulk operations."""
    conn = get_db_connection()

    # Check if results table exists and get its schema
    cursor = conn.execute("PRAGMA table_info(results)")
    existing_columns = [row[1] for row in cursor.fetchall()]

    if not existing_columns:
        # Create new results table with all fields
        conn.execute('''
            CREATE TABLE results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                cv_text TEXT,
                job_description TEXT,
                analysis TEXT,
                decision TEXT,
                rating INTEGER,
                batch_id TEXT,
                processing_time REAL,
                file_size INTEGER,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    else:
        # Add missing columns to existing table
        required_columns = {
            'decision': 'TEXT',
            'rating': 'INTEGER',
            'batch_id': 'TEXT',
            'processing_time': 'REAL',
            'file_size': 'INTEGER'
        }

        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    conn.execute(f'ALTER TABLE results ADD COLUMN {column} {column_type}')
                    logger.info(f"Added column {column} to results table")
                except Exception as e:
                    logger.warning(f"Could not add column {column}: {e}")

    # Create batch_jobs table for tracking bulk operations
    conn.execute('''
        CREATE TABLE IF NOT EXISTS batch_jobs (
            id TEXT PRIMARY KEY,
            total_files INTEGER,
            processed_files INTEGER,
            successful_files INTEGER,
            failed_files INTEGER,
            status TEXT,
            start_time DATETIME,
            end_time DATETIME,
            job_description TEXT
        )
    ''')

    # Create indexes for better performance (with error handling)
    indexes = [
        ('idx_results_timestamp', 'results', 'timestamp'),
        ('idx_results_batch_id', 'results', 'batch_id'),
        ('idx_results_decision', 'results', 'decision'),
        ('idx_results_filename', 'results', 'filename'),
        ('idx_batch_jobs_status', 'batch_jobs', 'status')
    ]

    for index_name, table_name, column_name in indexes:
        try:
            conn.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name})')
        except Exception as e:
            logger.warning(f"Could not create index {index_name}: {e}")

    conn.commit()
    conn.close()
    logger.info("Database initialization completed")

def store_analysis_result(filename, cv_text, job_description, analysis, batch_id=None, processing_time=None, file_size=None):
    """Store the analysis result in the database with enhanced metadata."""
    conn = get_db_connection()

    # Extract decision and rating from analysis with improved patterns
    decision = 'no'  # Default to 'no' instead of 'unknown'
    rating = None

    # Multiple patterns to extract decision
    decision_patterns = [
        r'Final Opinion:\s*(Yes|No)',
        r'Final Decision:\s*(Yes|No)',
        r'Recommendation:\s*(Yes|No)',
        r'Hire:\s*(Yes|No)'
    ]

    for pattern in decision_patterns:
        decision_match = re.search(pattern, analysis, re.IGNORECASE)
        if decision_match:
            decision = 'yes' if decision_match.group(1).lower() == 'yes' else 'no'
            break

    # Multiple patterns to extract rating
    rating_patterns = [
        r'Rating:\s*(\d+)/10',
        r'Rating:\s*(\d+)\s*out\s*of\s*10',
        r'Score:\s*(\d+)/10',
        r'(\d+)/10',
        r'rating.*?(\d+).*?out of (\d+)'
    ]

    for pattern in rating_patterns:
        rating_match = re.search(pattern, analysis, re.IGNORECASE)
        if rating_match:
            rating = int(rating_match.group(1))
            break

    # If no rating found, try to infer from decision and content
    if rating is None:
        if decision == 'yes':
            rating = 7  # Default good rating for yes decisions
        else:
            rating = 5  # Default lower rating for no decisions

    conn.execute('''
        INSERT INTO results (filename, cv_text, job_description, analysis, decision, rating, batch_id, processing_time, file_size)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (filename, cv_text, job_description, analysis, decision, rating, batch_id, processing_time, file_size))

    conn.commit()
    conn.close()

def store_batch_job(batch_id, total_files, job_description):
    """Store batch job information."""
    conn = get_db_connection()
    conn.execute('''
        INSERT INTO batch_jobs (id, total_files, processed_files, successful_files, failed_files, status, start_time, job_description)
        VALUES (?, ?, 0, 0, 0, 'processing', datetime('now'), ?)
    ''', (batch_id, total_files, job_description))
    conn.commit()
    conn.close()

def update_batch_job(batch_id, processed_files, successful_files, failed_files, status=None, end_time=None):
    """Update batch job progress."""
    conn = get_db_connection()
    if status and end_time:
        conn.execute('''
            UPDATE batch_jobs
            SET processed_files = ?, successful_files = ?, failed_files = ?, status = ?, end_time = datetime('now')
            WHERE id = ?
        ''', (processed_files, successful_files, failed_files, status, batch_id))
    else:
        conn.execute('''
            UPDATE batch_jobs
            SET processed_files = ?, successful_files = ?, failed_files = ?
            WHERE id = ?
        ''', (processed_files, successful_files, failed_files, batch_id))
    conn.commit()
    conn.close()

def extract_text_from_file(file_obj, filename):
    """Enhanced file text extraction with better error handling and memory management."""
    try:
        ext = filename.lower().rsplit('.', 1)[-1]
        file_size = 0

        if hasattr(file_obj, 'content_length'):
            file_size = file_obj.content_length

        if ext == 'pdf':
            try:
                # Reset file pointer
                file_obj.seek(0)
                pdf_reader = PdfReader(file_obj)
                text_parts = []

                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text() or ''
                        text_parts.append(page_text)
                    except Exception as e:
                        logger.warning(f"Error extracting page {page_num} from {filename}: {str(e)}")
                        continue

                cv_text = "\n".join(text_parts)
                return cv_text.strip(), file_size

            except Exception as e:
                logger.error(f"PDF extraction error for {filename}: {str(e)}")
                return None, file_size

        elif ext == 'txt':
            try:
                file_obj.seek(0)
                cv_text = file_obj.read().decode('utf-8', errors='ignore')
                return cv_text.strip(), file_size
            except Exception as e:
                logger.error(f"TXT extraction error for {filename}: {str(e)}")
                return None, file_size

        elif ext == 'docx':
            try:
                file_obj.seek(0)
                # Use temporary file for docx processing
                with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
                    tmp.write(file_obj.read())
                    tmp.flush()

                    try:
                        from docx import Document
                        doc = Document(tmp.name)
                        paragraphs = [para.text for para in doc.paragraphs if para.text.strip()]
                        cv_text = "\n".join(paragraphs)
                        return cv_text.strip(), file_size
                    finally:
                        # Clean up temp file
                        try:
                            os.unlink(tmp.name)
                        except:
                            pass

            except Exception as e:
                logger.error(f"DOCX extraction error for {filename}: {str(e)}")
                return None, file_size
        else:
            logger.error(f"Unsupported file type: {filename}")
            return None, file_size

    except Exception as e:
        logger.error(f"General extraction error for {filename}: {str(e)}")
        return None, 0

def post_process_response(response_text):
    """
    Post-process the response to ensure consistency.
    """
    final_opinion_match = re.search(r"Final Opinion:\s*(Yes|No)\s*-\s*(.+)", response_text)
    if final_opinion_match:
        decision = final_opinion_match.group(1)
        explanation = final_opinion_match.group(2)
        return f"Final Opinion: {decision} - {explanation}"
    return response_text  # Fallback if the format is not matched

def apply_decision_threshold(full_response_text):
    """
    Apply a decision threshold to ensure consistent final opinions based on rating.
    This should only be called on the complete response, not individual chunks.
    """
    # If the response is empty or too short, provide a fallback
    if not full_response_text or len(full_response_text.strip()) < 50:
        return """Rating:
5/10

Strengths:
- Unable to analyze due to processing error

Weaknesses:
- Analysis could not be completed

Final Opinion:
No - Unable to complete analysis, defaulting to rejection for safety."""

    # Extract rating with multiple patterns
    rating = None
    rating_patterns = [
        r'Rating:\s*(\d+)/10',
        r'Rating:\s*(\d+)\s*out\s*of\s*10',
        r'Score:\s*(\d+)/10',
        r'(\d+)/10'
    ]

    for pattern in rating_patterns:
        rating_match = re.search(pattern, full_response_text, re.IGNORECASE)
        if rating_match:
            rating = int(rating_match.group(1))
            break

    # If no rating found, try to infer from content or add a default
    if rating is None:
        # Check if there's a clear positive or negative sentiment
        if re.search(r'\b(excellent|outstanding|strong|qualified|recommended)\b', full_response_text, re.IGNORECASE):
            rating = 7
        elif re.search(r'\b(poor|weak|unqualified|lacking|insufficient)\b', full_response_text, re.IGNORECASE):
            rating = 4
        else:
            rating = 5  # Default neutral rating

        # Add rating to the response if it's missing
        if not re.search(r'Rating:', full_response_text, re.IGNORECASE):
            full_response_text = f"Rating:\n{rating}/10\n\n" + full_response_text

    # Ensure we have a final opinion
    if not re.search(r'Final Opinion:', full_response_text, re.IGNORECASE):
        decision = "Yes" if rating >= 7 else "No"
        explanation = f"Candidate scored {rating}/10, {'meets' if rating >= 7 else 'below'} our minimum threshold of 7/10."
        full_response_text += f"\n\nFinal Opinion:\n{decision} - {explanation}"

    # Apply rating threshold to existing decisions while preserving AI's reasoning
    if rating is not None:
        # Extract the AI's original explanation
        original_explanation = ""
        explanation_match = re.search(r"Final Opinion:\s*(Yes|No)\s*-\s*(.+)", full_response_text, re.IGNORECASE | re.DOTALL)
        if explanation_match:
            original_explanation = explanation_match.group(2).strip()

        # Only apply threshold logic if the AI's decision conflicts with the rating threshold
        ai_decision = explanation_match.group(1).lower() if explanation_match else None
        should_be_yes = rating >= 7
        should_be_no = rating < 7

        # Check if AI's decision conflicts with threshold
        decision_conflicts = (
            (should_be_yes and ai_decision == 'no') or
            (should_be_no and ai_decision == 'yes')
        )

        if decision_conflicts or not explanation_match:
            if rating >= 7:
                # Force Yes decision for ratings 7 and above
                if original_explanation and not original_explanation.startswith("Strong candidate with"):
                    # Preserve AI's reasoning but override decision
                    new_explanation = f"Yes - {original_explanation} (Note: Rating {rating}/10 meets our hiring threshold, overriding initial assessment)"
                else:
                    new_explanation = f"Yes - Strong candidate with {rating}/10 rating, meets our hiring threshold."

                full_response_text = re.sub(
                    r"Final Opinion:\s*(Yes|No)\s*-\s*(.+)",
                    f"Final Opinion: {new_explanation}",
                    full_response_text,
                    flags=re.IGNORECASE | re.DOTALL
                )
            else:
                # Force No decision for ratings below 7
                if original_explanation and not original_explanation.startswith("Candidate scored"):
                    # Preserve AI's reasoning but override decision
                    new_explanation = f"No - {original_explanation} (Note: Rating {rating}/10 is below our minimum threshold of 7/10)"
                else:
                    new_explanation = f"No - Candidate scored {rating}/10, below our minimum threshold of 7/10."

                full_response_text = re.sub(
                    r"Final Opinion:\s*(Yes|No)\s*-\s*(.+)",
                    f"Final Opinion: {new_explanation}",
                    full_response_text,
                    flags=re.IGNORECASE | re.DOTALL
                )
        # If AI's decision aligns with threshold, keep it as-is (no changes needed)

    return full_response_text

def generate_response_stream(cv_text, job_description_text):
    """
    Generator to stream AI response word by word using Ollama's HTTP API.
    Additionally, it accumulates the full analysis result and stores it in the DB once done.
    """
    prompt = f"""
    CV:
    {cv_text}

    Job Description:
    {job_description_text}

    Analyze the CV and job description. Provide the following:
    1. A rating (out of 10) for how well the CV matches the job description.
    2. A list of the candidate's **strengths** based on their CV.
    3. A list of the candidate's **weaknesses** based on their CV.
    4. Your final opinion on whether the candidate should be hired. Include a brief explanation for your decision.

    Additional Information:
    - CGPA (Cumulative Grade Point Average) is a measure of a student's academic performance. A CGPA of 3.4 or above is considered good, and a CGPA of 3.7 or above is considered excellent.
    - Do not show any bias in your evaluation. Be honest and transparent in your ratings and opinions.
    - For entry-level roles, prioritize academic performance, internships, and relevant projects.
    - For mid-level roles, prioritize work experience, technical skills, and certifications.
    - For senior-level roles, prioritize leadership experience, strategic thinking, and industry expertise.
    - If the candidate's skills match at least 70% of the job requirements, consider them a strong fit.
    - Certifications relevant to the job (e.g., PMP for project management, CPA for accounting) should be given higher weight.
    - Projects and internships should be evaluated based on their relevance to the job description.
    - If the job requires fluency in a specific language, ensure the candidate's CV demonstrates strong written and verbal communication skills.
    - If the company values innovation, look for evidence of creativity and problem-solving in the candidate's CV.
    - Gaps in employment history should be noted and considered in the evaluation.
    - Long-term employment at a single company may indicate loyalty and stability.
    - If the job is in healthcare, ensure the candidate has knowledge of medical terminology, patient care, or relevant certifications.
    - If the job is in finance, ensure the candidate has knowledge of financial regulations, accounting principles, or tools.
    - If the job is in marketing, ensure the candidate has knowledge of digital marketing tools or campaign management.
    - If the job requires teamwork, look for evidence of collaboration in the candidate's CV.
    - If the job requires leadership, look for evidence of leadership roles.
    - If the job requires problem-solving, look for examples of challenges the candidate has overcome.
    - If the candidate has participated in competitions, hackathons, or industry-specific events, consider it a positive indicator.
    - If the candidate has held leadership roles in student organizations or community groups, consider it a plus for roles requiring leadership skills.
    - If the job is remote, ensure the candidate has experience working remotely or has the necessary skills.
    - If the candidate has shown consistent career growth, consider it a strong positive.
    - If the rating is below 7/10 make sure to say no for the final opinion (even if it is at 6.5 it should be a no), if its above 7 say yes for the final opinion
    - If the rating final opinion is yes, make sure to explain why. If the final opinion is no, make sure to explain why.

    Format your response **exactly** as follows:
    Rating:
    <rating>/10

    Strengths:
    - <strength 1>
    - <strength 2>
    - ...

    Weaknesses:
    - <weakness 1>
    - <weakness 2>
    - ...

    Final Opinion:
    <Yes/No> - <brief explanation>
    """
    logger.debug(f"Prompt sent to Ollama: {prompt}")

    full_analysis = ""  # To accumulate the entire analysis result

    try:
        response = requests.post(
            OLLAMA_API_URL,
            json={
                "model": "llama3.2",
                "prompt": prompt,
                "stream": True,
                "seed": 42,
                "temperature": 0.2
            },
            stream=True
        )

        if response.status_code != 200:
            error_message = f"Failed to connect to Ollama API (Status Code: {response.status_code})"
            logger.error(error_message)
            yield "data: " + json.dumps({'error': error_message}) + "\n\n"
            return

        for line in response.iter_lines():
            if line:
                try:
                    response_data = json.loads(line.decode('utf-8'))
                    if 'response' in response_data:
                        chunk_text = response_data['response']
                        full_analysis += chunk_text
                        yield "data: " + json.dumps({'text': chunk_text}) + "\n\n"
                        yield " " * 2048 + "\n\n"
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode JSON: {e}")
                    continue

        # After streaming is complete, store the full result in the database
        # Note: For streaming, we don't have filename, so we'll skip storing here
        # The caller should handle storing the result with proper filename

    except Exception as e:
        error_message = f"Streaming error: {str(e)}"
        logger.error(error_message)
        yield "data: " + json.dumps({'error': error_message}) + "\n\n"

def analyze_cv_with_ai(cv_text, job_description_text, timeout=120):
    """
    Enhanced AI analysis function with better error handling and timeout management.
    Returns the full analysis text or None if failed.
    """
    if not cv_text or not cv_text.strip():
        return None, "Empty CV text"

    if len(cv_text) > 50000:  # Limit CV text length to prevent API issues
        cv_text = cv_text[:50000] + "... [truncated]"

    prompt = f"""
    CV:
    {cv_text}

    Job Description:
    {job_description_text}

    CRITICAL INSTRUCTIONS:
    You MUST analyze this CV against the job description and provide a structured response.
    You MUST provide a numerical rating and a clear Yes/No decision.

    RATING RULES:
    - Rate from 1-10 based on how well the CV matches the job requirements
    - Consider education, experience, skills, and cultural fit
    - Be objective and consistent in your evaluation

    DECISION RULES:
    - If rating is 7 or above: Final Opinion MUST be "Yes"
    - If rating is below 7: Final Opinion MUST be "No"
    - NO exceptions to this rule
    - ALWAYS provide detailed explanation for your decision

    EVALUATION CRITERIA:
    - CGPA 3.4+ is good, 3.7+ is excellent
    - Entry-level: Focus on education, internships, projects
    - Mid-level: Focus on work experience, technical skills, certifications
    - Senior-level: Focus on leadership, strategic thinking, industry expertise
    - 70%+ skill match = strong candidate

    FORMAT REQUIREMENTS:
    You MUST use this EXACT format - no deviations allowed:

    Rating:
    [NUMBER]/10

    Strengths:
    - [Specific strength 1]
    - [Specific strength 2]
    - [Specific strength 3]

    Weaknesses:
    - [Specific weakness 1]
    - [Specific weakness 2]
    - [Specific weakness 3]

    Final Opinion:
    [Yes/No] - [Detailed explanation including key factors that influenced your rating, specific strengths/weaknesses that led to this decision, and how well the candidate matches the job requirements]

    IMPORTANT:
    - You MUST include a rating number
    - You MUST say either "Yes" or "No" in Final Opinion
    - Be specific in strengths and weaknesses
    - Explain your decision clearly
    """

    try:
        payload = {
            "model": "llama3.2",
            "prompt": prompt,
            "stream": False,  # Non-streaming for batch processing
            "seed": 42,
            "temperature": 0.2
        }

        response = requests.post(OLLAMA_API_URL, json=payload, timeout=timeout)
        response.raise_for_status()

        response_data = response.json()
        if 'response' in response_data:
            analysis = response_data['response']
            # Apply post-processing and threshold only once on the complete response
            processed_analysis = apply_decision_threshold(analysis)
            return processed_analysis, None
        else:
            return None, "No response from AI model"

    except requests.exceptions.Timeout:
        return None, f"AI analysis timeout after {timeout} seconds"
    except requests.exceptions.RequestException as e:
        return None, f"AI API error: {str(e)}"
    except json.JSONDecodeError as e:
        return None, f"Invalid JSON response: {str(e)}"
    except Exception as e:
        return None, f"Unexpected error: {str(e)}"

def process_single_file(file_data, job_description, batch_id, session_id):
    """
    Process a single file and return the result.
    This function is designed to be run in a separate thread.
    """
    filename = file_data['filename']
    file_content = file_data['content']

    start_time = time.time()

    try:
        # Extract text from file
        cv_text, file_size = extract_text_from_file(BytesIO(file_content), filename)

        if cv_text is None:
            return {
                'filename': filename,
                'error': 'Failed to extract text from file',
                'processing_time': time.time() - start_time
            }

        if not cv_text.strip():
            return {
                'filename': filename,
                'error': 'Empty file content',
                'processing_time': time.time() - start_time
            }

        # Analyze with AI
        analysis, error = analyze_cv_with_ai(cv_text, job_description)

        if error:
            return {
                'filename': filename,
                'error': f'AI analysis failed: {error}',
                'processing_time': time.time() - start_time
            }

        processing_time = time.time() - start_time

        # Store result in database
        store_analysis_result(filename, cv_text, job_description, analysis, batch_id, processing_time, file_size)

        # Determine decision with improved patterns
        decision = 'no'  # Default to 'no' instead of 'unknown'
        decision_patterns = [
            r'Final Opinion:\s*(Yes|No)',
            r'Final Decision:\s*(Yes|No)',
            r'Recommendation:\s*(Yes|No)',
            r'Hire:\s*(Yes|No)'
        ]

        for pattern in decision_patterns:
            decision_match = re.search(pattern, analysis, re.IGNORECASE)
            if decision_match:
                decision = 'yes' if decision_match.group(1).lower() == 'yes' else 'no'
                break

        # Emit progress update via SocketIO
        socketio.emit('progress_update', {
            'filename': filename,
            'decision': decision,
            'processing_time': processing_time
        }, room=session_id)

        return {
            'filename': filename,
            'decision': decision,
            'processing_time': processing_time
        }

    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f'Processing error: {str(e)}'
        logger.error(f"Error processing {filename}: {error_msg}")

        return {
            'filename': filename,
            'error': error_msg,
            'processing_time': processing_time
        }
    finally:
        # Force garbage collection to free memory
        gc.collect()

# --- Routes ---

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload():
    """
    Enhanced endpoint to handle bulk CV upload with async processing and progress tracking.
    """
    try:
        cv_files = request.files.getlist('cv')
        job_description_text = request.form.get('job_description')

        if not cv_files or not job_description_text:
            return jsonify({'error': 'CV file(s) and Job Description text are required'})

        # Generate unique batch ID
        batch_id = str(uuid.uuid4())
        session_id = request.form.get('session_id', batch_id)

        # Validate files and prepare file data
        valid_files = []
        invalid_files = []

        for cv_file in cv_files:
            filename = cv_file.filename
            if not filename:
                continue

            if not allowed_file(filename):
                invalid_files.append({'filename': filename, 'error': 'Invalid file type'})
                continue

            try:
                # Read file content into memory
                file_content = cv_file.read()
                if len(file_content) == 0:
                    invalid_files.append({'filename': filename, 'error': 'Empty file'})
                    continue

                valid_files.append({
                    'filename': filename,
                    'content': file_content
                })
            except Exception as e:
                invalid_files.append({'filename': filename, 'error': f'File read error: {str(e)}'})
                continue

        if not valid_files:
            return jsonify({'error': 'No valid files to process', 'invalid_files': invalid_files})

        total_files = len(valid_files)
        logger.info(f"Starting batch processing: {total_files} files, batch_id: {batch_id}")

        # Store batch job in database
        store_batch_job(batch_id, total_files, job_description_text)

        # Start background processing
        def process_files_async():
            try:
                processed_files = 0
                successful_files = 0
                failed_files = 0
                results_summary = []

                # Process files in batches to manage memory
                for i in range(0, len(valid_files), BATCH_SIZE):
                    batch_files = valid_files[i:i + BATCH_SIZE]

                    # Use ThreadPoolExecutor for concurrent processing
                    with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_WORKERS) as executor:
                        # Submit all files in current batch
                        future_to_file = {
                            executor.submit(process_single_file, file_data, job_description_text, batch_id, session_id): file_data
                            for file_data in batch_files
                        }

                        # Process completed futures
                        for future in as_completed(future_to_file):
                            file_data = future_to_file[future]
                            try:
                                result = future.result()
                                results_summary.append(result)

                                processed_files += 1
                                if 'error' in result:
                                    failed_files += 1
                                else:
                                    successful_files += 1

                                # Update batch progress
                                update_batch_job(batch_id, processed_files, successful_files, failed_files)

                                # Emit progress update
                                progress_percent = (processed_files / total_files) * 100
                                socketio.emit('batch_progress', {
                                    'batch_id': batch_id,
                                    'processed': processed_files,
                                    'total': total_files,
                                    'successful': successful_files,
                                    'failed': failed_files,
                                    'progress_percent': progress_percent
                                }, room=session_id)

                            except Exception as e:
                                logger.error(f"Error processing file {file_data['filename']}: {str(e)}")
                                failed_files += 1
                                processed_files += 1
                                results_summary.append({
                                    'filename': file_data['filename'],
                                    'error': f'Processing failed: {str(e)}'
                                })

                    # Force garbage collection between batches
                    gc.collect()

                # Mark batch as completed
                update_batch_job(batch_id, processed_files, successful_files, failed_files, 'completed', True)

                # Send final completion notification
                socketio.emit('batch_complete', {
                    'batch_id': batch_id,
                    'total': total_files,
                    'successful': successful_files,
                    'failed': failed_files,
                    'results': results_summary
                }, room=session_id)

                logger.info(f"Batch processing completed: {batch_id}, {successful_files}/{total_files} successful")

            except Exception as e:
                logger.error(f"Batch processing error: {str(e)}")
                update_batch_job(batch_id, processed_files, successful_files, failed_files, 'failed', True)
                socketio.emit('batch_error', {
                    'batch_id': batch_id,
                    'error': str(e)
                }, room=session_id)

        # Start async processing in a separate thread
        processing_thread = threading.Thread(target=process_files_async)
        processing_thread.daemon = True
        processing_thread.start()

        # Store job info for tracking
        active_jobs[batch_id] = {
            'total_files': total_files,
            'start_time': time.time(),
            'session_id': session_id,
            'thread': processing_thread
        }

        return jsonify({
            'batch_id': batch_id,
            'total_files': total_files,
            'invalid_files': invalid_files,
            'message': 'Batch processing started. You will receive real-time updates.',
            'session_id': session_id
        })

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        return jsonify({'error': f'Upload error: {str(e)}'})

@app.route('/upload_single', methods=['POST'])
def upload_single():
    """
    Original single-file upload endpoint with streaming response.
    """
    try:
        cv_files = request.files.getlist('cv')
        job_description_text = request.form.get('job_description')

        if not cv_files or not job_description_text:
            return Response(
                json.dumps({'error': 'CV file(s) and Job Description text are required'}),
                content_type='application/json'
            )

        results_summary = []
        for cv_file in cv_files:
            filename = cv_file.filename
            if not allowed_file(filename):
                results_summary.append({'filename': filename, 'error': 'Invalid file type'})
                continue

            # Extract text using enhanced function
            cv_text, file_size = extract_text_from_file(cv_file, filename)

            if cv_text is None:
                results_summary.append({'filename': filename, 'error': 'Failed to extract text from file'})
                continue

            # Run AI analysis (blocking for now)
            try:
                full_analysis = ""
                ai_error = None
                for chunk in generate_response_stream(cv_text, job_description_text):
                    try:
                        chunk = chunk.replace('data: ', '').strip()
                        if not chunk:
                            continue
                        data = json.loads(chunk)
                        if 'text' in data:
                            full_analysis += data['text']
                        elif 'error' in data:
                            ai_error = data['error']
                            break
                    except Exception as e:
                        ai_error = f'AI/JSON error: {str(e)}'
                        break

                if ai_error:
                    results_summary.append({'filename': filename, 'error': ai_error})
                    continue

                if not full_analysis.strip():
                    results_summary.append({'filename': filename, 'error': 'No analysis returned from AI.'})
                    continue

                # Apply post-processing to the complete analysis
                processed_analysis = apply_decision_threshold(full_analysis)
                store_analysis_result(filename, cv_text, job_description_text, processed_analysis, file_size=file_size)

                # Determine decision with improved patterns
                decision = 'no'  # Default to 'no' instead of 'unknown'
                decision_patterns = [
                    r'Final Opinion:\s*(Yes|No)',
                    r'Final Decision:\s*(Yes|No)',
                    r'Recommendation:\s*(Yes|No)',
                    r'Hire:\s*(Yes|No)'
                ]

                for pattern in decision_patterns:
                    decision_match = re.search(pattern, processed_analysis, re.IGNORECASE)
                    if decision_match:
                        decision = 'yes' if decision_match.group(1).lower() == 'yes' else 'no'
                        break

                results_summary.append({'filename': filename, 'decision': decision})

            except Exception as e:
                results_summary.append({'filename': filename, 'error': f'Analysis error: {str(e)}'})

        return Response(json.dumps({'results': results_summary}), content_type='application/json')

    except Exception as e:
        return Response(
            json.dumps({'error': f'Upload error: {str(e)}'}),
            content_type='application/json'
        )

# --- SocketIO Event Handlers ---

@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    logger.info(f"Client connected: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    logger.info(f"Client disconnected: {request.sid}")

@socketio.on('join_session')
def handle_join_session(data):
    """Join a session room for receiving updates."""
    session_id = data.get('session_id')
    if session_id:
        join_room(session_id)
        logger.info(f"Client {request.sid} joined session {session_id}")

# --- Batch Management Routes ---

@app.route('/batch_status/<batch_id>')
def batch_status(batch_id):
    """Get the current status of a batch job."""
    try:
        conn = get_db_connection()
        batch = conn.execute('SELECT * FROM batch_jobs WHERE id = ?', (batch_id,)).fetchone()
        conn.close()

        if not batch:
            return jsonify({'error': 'Batch not found'}), 404

        return jsonify({
            'batch_id': batch['id'],
            'total_files': batch['total_files'],
            'processed_files': batch['processed_files'],
            'successful_files': batch['successful_files'],
            'failed_files': batch['failed_files'],
            'status': batch['status'],
            'start_time': batch['start_time'],
            'end_time': batch['end_time']
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/batch_results/<batch_id>')
def batch_results(batch_id):
    """Get all results for a specific batch."""
    try:
        conn = get_db_connection()
        results = conn.execute(
            'SELECT * FROM results WHERE batch_id = ? ORDER BY timestamp DESC',
            (batch_id,)
        ).fetchall()
        conn.close()

        results_list = []
        for result in results:
            results_list.append({
                'id': result['id'],
                'filename': result['filename'],
                'decision': result['decision'],
                'rating': result['rating'],
                'processing_time': result['processing_time'],
                'file_size': result['file_size'],
                'timestamp': result['timestamp']
            })

        return jsonify({'results': results_list})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/active_batches')
def active_batches():
    """Get list of all active batch jobs."""
    try:
        conn = get_db_connection()
        batches = conn.execute(
            "SELECT * FROM batch_jobs WHERE status IN ('processing', 'pending') ORDER BY start_time DESC"
        ).fetchall()
        conn.close()

        batch_list = []
        for batch in batches:
            batch_list.append({
                'batch_id': batch['id'],
                'total_files': batch['total_files'],
                'processed_files': batch['processed_files'],
                'successful_files': batch['successful_files'],
                'failed_files': batch['failed_files'],
                'status': batch['status'],
                'start_time': batch['start_time']
            })

        return jsonify({'batches': batch_list})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/active_batches_page')
def active_batches_page():
    """Render the active batches page."""
    return render_template('active_batches.html')

# --- New Routes for Results Management ---

@app.route('/results')
def results():
    """List all stored analysis results with pagination and filtering."""
    page = request.args.get('page', 1, type=int)
    per_page = 50  # Show 50 results per page
    batch_id = request.args.get('batch_id')
    decision_filter = request.args.get('decision')

    conn = get_db_connection()

    # Build query with filters
    query = 'SELECT id, filename, job_description, analysis, decision, rating, batch_id, processing_time, timestamp FROM results'
    params = []
    conditions = []

    if batch_id:
        conditions.append('batch_id = ?')
        params.append(batch_id)

    if decision_filter and decision_filter != 'all':
        conditions.append('decision = ?')
        params.append(decision_filter)

    if conditions:
        query += ' WHERE ' + ' AND '.join(conditions)

    query += ' ORDER BY timestamp DESC'

    # Get total count for pagination
    count_query = query.replace('SELECT id, filename, job_description, analysis, decision, rating, batch_id, processing_time, timestamp FROM results', 'SELECT COUNT(*) FROM results')
    total = conn.execute(count_query, params).fetchone()[0]

    # Add pagination
    offset = (page - 1) * per_page
    query += f' LIMIT {per_page} OFFSET {offset}'

    results = conn.execute(query, params).fetchall()

    # Get batch information for filtering
    batches = conn.execute('SELECT DISTINCT batch_id FROM results WHERE batch_id IS NOT NULL ORDER BY batch_id DESC').fetchall()

    conn.close()

    # Calculate pagination info
    total_pages = (total + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages

    return render_template('results.html',
                         results=results,
                         batches=batches,
                         current_page=page,
                         total_pages=total_pages,
                         has_prev=has_prev,
                         has_next=has_next,
                         batch_id=batch_id,
                         decision_filter=decision_filter,
                         total_results=total)

@app.route('/result/<int:result_id>')
def view_result(result_id):
    """View full details of a single analysis result."""
    conn = get_db_connection()
    result = conn.execute('SELECT * FROM results WHERE id = ?', (result_id,)).fetchone()
    conn.close()
    if result is None:
        return "Result not found", 404
    fixed_job_desc = detokenize_text(result['job_description'])
    fixed_analysis = detokenize_text(result['analysis'])
    return render_template('result.html', result=result, fixed_job_desc=fixed_job_desc, fixed_analysis=fixed_analysis)


def parse_analysis_for_pdf(analysis_text):
    """Parse analysis text into structured components for PDF generation."""
    lines = analysis_text.split('\n')
    rating = None
    strengths = []
    weaknesses = []
    final_opinion = ''

    current_section = ''

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Extract rating
        rating_match = re.search(r'Rating:\s*(\d+)/10', line, re.IGNORECASE)
        if rating_match:
            rating = int(rating_match.group(1))
            continue

        # Detect sections
        if line.lower().startswith('strengths:'):
            current_section = 'strengths'
            continue
        elif line.lower().startswith('weaknesses:'):
            current_section = 'weaknesses'
            continue
        elif line.lower().startswith('final opinion:'):
            current_section = 'opinion'
            final_opinion = line.replace('Final Opinion:', '').strip()
            continue

        # Add items to current section
        if line.startswith('-') or line.startswith('•'):
            item = line[1:].strip()  # Fix: use slice notation instead of substring
            if current_section == 'strengths':
                strengths.append(item)
            elif current_section == 'weaknesses':
                weaknesses.append(item)
        elif current_section == 'opinion' and line:
            final_opinion += ' ' + line

    return {
        'rating': rating,
        'strengths': strengths,
        'weaknesses': weaknesses,
        'final_opinion': final_opinion
    }

@app.route('/download/<int:result_id>')
def download_result(result_id):
    """Download the analysis result as a beautiful PDF matching the web interface."""
    conn = get_db_connection()
    result = conn.execute('SELECT * FROM results WHERE id = ?', (result_id,)).fetchone()
    conn.close()
    if result is None:
        return "Result not found", 404

    from io import BytesIO
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.colors import HexColor, white, black
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from flask import Response

    # Parse the analysis
    fixed_analysis = detokenize_text(result['analysis'])
    parsed = parse_analysis_for_pdf(fixed_analysis)

    buffer = BytesIO()
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        rightMargin=50,
        leftMargin=50,
        topMargin=50,
        bottomMargin=50
    )

    # Custom styles
    styles = getSampleStyleSheet()

    # Define custom colors
    primary_color = HexColor('#2563eb')
    success_color = HexColor('#10b981')
    danger_color = HexColor('#ef4444')
    gray_color = HexColor('#6b7280')

    # Custom paragraph styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Title'],
        fontSize=24,
        textColor=primary_color,
        spaceAfter=20,
        alignment=1  # Center alignment
    )

    header_style = ParagraphStyle(
        'CustomHeader',
        parent=styles['Heading2'],
        fontSize=16,
        textColor=primary_color,
        spaceBefore=20,
        spaceAfter=10,
        borderWidth=0,
        borderColor=primary_color,
        borderPadding=5
    )

    body_style = ParagraphStyle(
        'CustomBody',
        parent=styles['Normal'],
        fontSize=11,
        leading=14,
        spaceAfter=8
    )

    flowables = []

    # Header Section
    flowables.append(Paragraph(f"CV Analysis Report #{result['id']}", title_style))
    flowables.append(Spacer(1, 10))

    # Metadata table
    metadata_data = [
        ['Report ID:', f"#{result['id']}"],
        ['Generated:', result['timestamp']],
        ['Filename:', result['filename'] or 'N/A'],
        ['Processing Time:', f"{result['processing_time']:.2f}s" if result['processing_time'] else 'N/A']
    ]

    metadata_table = Table(metadata_data, colWidths=[1.5*inch, 4*inch])
    metadata_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('TEXTCOLOR', (0, 0), (0, -1), gray_color),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
    ]))
    flowables.append(metadata_table)
    flowables.append(Spacer(1, 20))

    # Rating Section
    if parsed['rating'] is not None:
        rating_color = success_color if parsed['rating'] >= 7 else danger_color if parsed['rating'] < 5 else HexColor('#f59e0b')

        # Create rating display
        rating_data = [
            [f"{parsed['rating']}/10", "Overall Match Score"]
        ]

        rating_table = Table(rating_data, colWidths=[1.5*inch, 4*inch])
        rating_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (0, 0), 36),
            ('TEXTCOLOR', (0, 0), (0, 0), rating_color),
            ('FONTNAME', (1, 0), (1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (1, 0), (1, 0), 14),
            ('TEXTCOLOR', (1, 0), (1, 0), gray_color),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('BACKGROUND', (0, 0), (-1, -1), HexColor('#f9fafb')),
            ('BOX', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
            ('ROUNDEDCORNERS', (0, 0), (-1, -1), 8),
        ]))
        flowables.append(rating_table)
        flowables.append(Spacer(1, 20))

    # Job Description Section
    flowables.append(Paragraph("📋 Job Description", header_style))
    fixed_job_desc = detokenize_text(result['job_description'])
    job_desc_para = Paragraph(fixed_job_desc.replace('\n', '<br/>'), body_style)
    flowables.append(job_desc_para)
    flowables.append(Spacer(1, 20))

    # Strengths Section
    if parsed['strengths']:
        flowables.append(Paragraph("💪 Strengths", header_style))
        for strength in parsed['strengths']:
            strength_para = Paragraph(f"✓ {strength}", body_style)
            flowables.append(strength_para)
        flowables.append(Spacer(1, 15))

    # Weaknesses Section
    if parsed['weaknesses']:
        flowables.append(Paragraph("⚠️ Areas for Improvement", header_style))
        for weakness in parsed['weaknesses']:
            weakness_para = Paragraph(f"⚠ {weakness}", body_style)
            flowables.append(weakness_para)
        flowables.append(Spacer(1, 15))

    # Final Decision Section
    if parsed['final_opinion']:
        flowables.append(Paragraph("🎯 Final Recommendation", header_style))

        # Determine decision styling
        is_positive = 'yes' in parsed['final_opinion'].lower()
        decision_color = success_color if is_positive else danger_color
        decision_icon = "✅" if is_positive else "❌"

        decision_para = Paragraph(
            f"{decision_icon} {parsed['final_opinion']}",
            ParagraphStyle(
                'DecisionStyle',
                parent=body_style,
                textColor=decision_color,
                fontSize=12,
                fontName='Helvetica-Bold'
            )
        )
        flowables.append(decision_para)

    # Footer
    flowables.append(Spacer(1, 30))
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=8,
        textColor=gray_color,
        alignment=1  # Center alignment
    )
    flowables.append(Paragraph("Generated by CVScreenify - Professional CV Analysis System", footer_style))

    # Build PDF
    doc.build(flowables)
    pdf = buffer.getvalue()
    buffer.close()

    response = Response(pdf, mimetype='application/pdf')
    response.headers.set('Content-Disposition', 'attachment', filename=f'result_{result["id"]}.pdf')
    return response


@app.route('/delete/<int:result_id>')
def delete_result(result_id):
    """Delete a single analysis result."""
    conn = get_db_connection()
    conn.execute('DELETE FROM results WHERE id = ?', (result_id,))
    conn.commit()
    conn.close()
    return redirect(url_for('results'))

@app.route('/delete_all')
def delete_all():
    conn = get_db_connection()
    conn.execute('DELETE FROM results')
    conn.commit()
    conn.close()
    return redirect(url_for('results'))

# --- Initialize Database and Run App ---
init_db()

if __name__ == '__main__':
    # Use SocketIO for real-time communication
    socketio.run(app, debug=True, host='0.0.0.0', port=5000, allow_unsafe_werkzeug=True)
